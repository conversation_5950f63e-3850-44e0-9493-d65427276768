{"name": "ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint --ext .js,.jsx,.ts,.tsx .", "format": "prettier --write ."}, "dependencies": {"@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-slot": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.501.0", "motion": "^12.7.4", "next": "15.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.27.0", "eslint-config-next": "15.3.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "prettier": "^3.5.3", "tailwindcss": "^4", "typescript": "^5"}}