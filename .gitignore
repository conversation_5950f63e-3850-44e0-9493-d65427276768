# .gitignore (in your-project-root/apps/backend/)

# Virtual Environment
venv/
.venv/
env/
ENV/
activate_this.py

# Python Bytecode and Caches
__pycache__/
*.py[cod]
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
uv.lock

# SQLite databases
*.sqlite3
*.db

# Test artifacts
.pytest_cache/
.coverage
coverage.xml
htmlcov/

# Local instance or config files specific to backend
instance/
.env # Backend specific environment variables

# cursor related files
.cursor/
.cursorrules

.DS_Store


# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
