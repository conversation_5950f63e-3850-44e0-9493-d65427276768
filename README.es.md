<div align="center">

[![Resume Matcher](assets/page_2.png)](https://www.resumematcher.fyi)

# Resume Matcher


[Discord](https://dsc.gg/resume-matcher) ✦ [𝚂𝚒𝚝𝚒𝚘 𝚆𝚎𝚋](https://resumematcher.fyi) ✦ [𝙲𝚘́𝚖𝚘 𝙸𝚗𝚜𝚝𝚊𝚕𝚊𝚛](#cómo-instalar) ✦ [𝙲𝚘𝚕𝚊𝚋𝚘𝚛𝚊𝚍𝚘𝚛𝚎𝚜](#colaboradores) ✦ [𝙳𝚘𝚗𝚊𝚛](#apoya-el-desarrollo-donando) ✦ [𝚃𝚠𝚒𝚝𝚝𝚎𝚛/𝚇](https://twitter.com/_srbhr_) ✦ [𝙻𝚒𝚗𝚔𝚎𝚍𝙸𝚗](https://www.linkedin.com/company/resume-matcher/)

**Deja de ser rechazado automáticamente por los bots ATS.** Resume Matcher es la plataforma impulsada por IA que aplica ingeniería inversa a los algoritmos de contratación para mostrarte exactamente cómo adaptar tu currículum. Obtén las palabras clave, el formato y los conocimientos que realmente te ayudarán a superar el primer filtro y llegar a manos humanas.

Esperamos convertir esto en **el VS Code para crear currículums**.

</div>

<br>

<div align="center">

![Stars](https://img.shields.io/github/stars/srbhr/Resume-Matcher?labelColor=black&style=for-the-badge&color=c20a71)
![Apache 2.0](https://img.shields.io/github/license/srbhr/Resume-Matcher?labelColor=black&style=for-the-badge&color=c20a71) ![Forks](https://img.shields.io/github/forks/srbhr/Resume-Matcher?labelColor=black&style=for-the-badge&color=c20a71) ![version](https://img.shields.io/badge/Version-0.1%20Veridis%20Quo-FFF?labelColor=black&logo=LinkedIn&style=for-the-badge&color=c20a71)


[![Discord](https://img.shields.io/discord/1122069176962531400?labelColor=black&logo=discord&logoColor=c20a71&style=for-the-badge&color=c20a71)](https://dsc.gg/resume-matcher) [![Sitio Web](https://img.shields.io/badge/website-Resume%20Matcher-FFF?labelColor=black&style=for-the-badge&color=c20a71)](https://resumematcher.fyi) [![LinkedIn](https://img.shields.io/badge/LinkedIn-Resume%20Matcher-FFF?labelColor=black&logo=LinkedIn&style=for-the-badge&color=c20a71)](https://www.linkedin.com/company/resume-matcher/)

<a href="https://trendshift.io/repositories/565" target="_blank"><img src="https://trendshift.io/api/badge/repositories/565" alt="srbhr%2FResume-Matcher | Trendshift" style="width: 250px; height: 55px;" width="250" height="55"/></a>

</div>

> \[!IMPORTANT]
>
> Este proyecto está en desarrollo activo. Se están añadiendo nuevas características continuamente y agradecemos las contribuciones de la comunidad. Hay algunos cambios importantes en la rama `main`. Si tienes alguna sugerencia o solicitud de características, no dudes en abrir un *issue* en GitHub o discutirlo en nuestro servidor de [Discord](https://dsc.gg/resume-matcher).


## Primeros pasos con Resume Matcher

Resume Matcher está diseñado para ayudarte a optimizar tu currículum con el objetivo de resaltar tus habilidades y experiencia de una manera que resuene con los empleadores potenciales.

Estamos trabajando para mejorar activamente la plataforma, con el objetivo de construir **el VS Code para crear currículums**. La mejor manera de mantenerte actualizado es unirte a la discusión en Discord y ser parte de la comunidad de desarrollo activo.

> Únete a nuestra comunidad de [Discord](https://dsc.gg/resume-matcher) 👇
[![Discord](assets/resume_matcher_discord.png)](https://dsc.gg/resume-matcher)

> Síguenos en [LinkedIn](https://www.linkedin.com/company/resume-matcher/) ✨
[![LinkedIn](assets/resume_matcher_linkedin.png)](https://www.linkedin.com/company/resume-matcher/)

> ⭐ Dale una estrella a Resume Matcher para apoyar el desarrollo y recibir actualizaciones en GitHub.
![Dale una estrella a Resume Matcher](assets/star_resume_matcher.png)

## Características Principales

![Características de Resume Matcher](assets/resume_matcher_features.png)

- **Funciona localmente**: No es necesario subir tu currículum a un servidor. Todo se ejecuta en tu máquina con modelos de IA de código abierto de Ollama.
- **Compatibilidad con ATS**: Obtén un análisis detallado de la compatibilidad de tu currículum con los sistemas ATS.
- **Puntuación de Coincidencia Instantánea**: Sube tu currículum y la descripción del trabajo para obtener una puntuación de coincidencia rápida y áreas clave de mejora.
- **Optimizador de Palabras Clave**: Alinea tu currículum con las palabras clave del trabajo e identifica potenciales mejoras.
- **Mejoras Guiadas**: Recibe sugerencias claras para que tu currículum resalte.

### Roadmap

Si tienes alguna sugerencia o solicitud de características, no dudes en abrir un *issue* en GitHub y discutirlo en nuestro servidor de [Discord](https://dsc.gg/resume-matcher).

- Resaltado visual de palabras clave.
- Lienzo de IA (AI Canvas), que puede ayudar a crear contenido de currículum impactante y basado en métricas.
- Optimización para múltiples descripciones de trabajo.

## Cómo Instalar

![Instalación](assets/how_to_install_resumematcher.png)

Sigue las instrucciones en el archivo [SETUP.es.md](SETUP.es.md) para configurar el proyecto localmente. El script de configuración instalará todas las dependencias necesarias y configurará tu entorno.

El proyecto está construido usando:

- FastAPI para el backend.
- Next.js para el frontend.
- Ollama para modelos de IA locales.
- Tailwind CSS para los estilos.
- SQLite para la base de datos.

| Tecnología   | Info/Versión                                  |
|--------------|-----------------------------------------------|
| Python       | 3.12+                                         |
| Next.js      | 15+                                           |
| Ollama       | 0.6.7                                         |


## Únete y Contribuye

![Cómo contribuir](assets/how_to_contribute.png)

¡Damos la bienvenida a las contribuciones de todos! Ya seas un desarrollador, diseñador o simplemente alguien que quiere ayudar. Todos los colaboradores están listados en la [página "Acerca de"](https://resumematcher.fyi/about) en nuestro sitio web y en el Readme de GitHub.

Echa un vistazo al roadmap si te gustaría trabajar en las características que están planeadas para el futuro. Si tienes alguna sugerencia o solicitud de características, no dudes en abrir un *issue* en GitHub y discutirlo en nuestro servidor de [Discord](https://dsc.gg/resume-matcher).

## Colaboradores
![Colaboradores](assets/contributors.png)

<a href="https://github.com/srbhr/Resume-Matcher/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=srbhr/Resume-Matcher" />
</a>

## Apoya el Desarrollo Donando
![Apoyanos](assets/supporting_resume_matcher.png)

Si te gustaría apoyar el desarrollo de Resume Matcher, puedes hacerlo donando. Tus contribuciones nos ayudarán a mantener el proyecto vivo y a seguir añadiendo nuevas funciones.

| Plataforma  | Enlace                                   |
|-----------|----------------------------------------|
| GitHub    | [![Patrocinadores de Github](https://img.shields.io/github/sponsors/srbhr?style=for-the-badge&color=c20a71&labelColor=black&logo=github)](https://github.com/sponsors/srbhr) |
| Buy Me a Coffee | [![BuyMeACoffee](https://img.shields.io/badge/Buy%20Me%20a%20Coffee-ffdd00?style=for-the-badge&logo=buy-me-a-coffee&color=c20a72&logoColor=white)](https://www.buymeacoffee.com/srbhr) |

<details>
  <summary><kbd>Historial de Estrellas</kbd></summary>
  <picture>
    <source media="(prefers-color-scheme: dark)" srcset="https://api.star-history.com/svg?repos=srbhr/resume-matcher&theme=dark&type=Date">
    <img width="100%" src="https://api.star-history.com/svg?repos=srbhr/resume-matcher&theme=dark&type=Date">
  </picture>
</details>